import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { db } from "../models";
import { RecipeStatus } from "../models/Recipe";
import { RecipeAttributesStatus } from "../models/RecipeAttributes";
import { RecipeIngredientsStatus } from "../models/RecipeIngredients";
import { RecipeStepsStatus } from "../models/RecipeSteps";
import { RecipeResourceStatus } from "../models/RecipeResources";
import { RecipeHistoryAction } from "../models/RecipeHistory";
import { generateUniqueSlug } from "../helper/slugGenerator";


// Get models from db object to ensure associations are set up
const Recipe = db.Recipe;
const RecipeCategory = db.RecipeCategory;
const RecipeAttributes = db.RecipeAttributes;
const RecipeIngredients = db.RecipeIngredients;
const RecipeSteps = db.RecipeSteps;
const RecipeResources = db.RecipeResources;
// const RecipeHistory = db.RecipeHistory; // Imported via helper functions


import { createRecipeHistory } from "../helper/recipe.helper";
import {
  updateRecipeCostTimestamp,
  updateRecipeNutritionTimestamp,
} from "../helper/timestamp.helper";
import {
  TransactionManager,
  ErrorHandler,
} from "../helper/transaction.helper";
import { ValidationHelper } from "../helper/validation.helper";
import {
  getRecipeHighlight,
  hasRecentChanges,
} from "../helper/recipe-highlight.helper";
import settingsService from "../services/settings.service";
import { getPlatformFromRequest, validateModulePermission } from "../helper/common";
import { MODULE_SLUGS, PERMISSION_TYPES } from "../helper/constant";


// ============================================================================
// CONSTANTS AND SHARED UTILITIES
// ============================================================================

// Constants for batch processing (kept for backward compatibility)
// Note: These constants are currently unused but kept for potential future use

// Helper function to capture old recipe data for history tracking
const captureOldRecipeData = async (recipeId: number, transaction: any) => {
  try {
    // Get basic recipe data
    const oldRecipe = await Recipe.findByPk(recipeId, {
      transaction,
      raw: true,
    });

    if (!oldRecipe) {
      return null;
    }

    // Get related data for comprehensive history tracking
    const [ingredients, steps, categories, attributes, resources] = await Promise.all([
      // Get ingredients
      RecipeIngredients.findAll({
        where: { recipe_id: recipeId },
        transaction,
        raw: true,
      }),
      // Get steps
      RecipeSteps.findAll({
        where: { recipe_id: recipeId },
        order: [['recipe_step_order', 'ASC']],
        transaction,
        raw: true,
      }),
      // Get categories
      RecipeCategory.findAll({
        where: { recipe_id: recipeId },
        transaction,
        raw: true,
      }),
      // Get attributes with type information
      db.sequelize.query(`
        SELECT ra.*, fa.attribute_type
        FROM mo_recipe_attributes ra
        LEFT JOIN mo_food_attributes fa ON ra.attributes_id = fa.id
        WHERE ra.recipe_id = :recipeId AND ra.status = 'active'
      `, {
        replacements: { recipeId },
        type: db.sequelize.QueryTypes.SELECT,
        transaction,
      }),
      // Get resources
      RecipeResources.findAll({
        where: { recipe_id: recipeId, status: 'active' },
        transaction,
        raw: true,
      }),
    ]);

    return {
      ...oldRecipe,
      ingredients: ingredients || [],
      steps: steps || [],
      categories: categories || [],
      attributes: attributes || [],
      resources: resources || [],
    };
  } catch (error) {
    console.error("Error capturing old recipe data:", error);
    return null;
  }
};

// Helper function to enhance response with highlight data
const enhanceResponseWithHighlights = async (
  recipeId: number,
  organizationId: string,
  baseResponse: any
) => {
  try {
    // Get organization settings to check if highlights are allowed
    const organizationSettings = await settingsService.getStructuredSettingsByOrganizationId(organizationId);
    const isHighlightedAllowed = organizationSettings.privateRecipeVisibilitySettings.highlightChanges;

    if (!isHighlightedAllowed) {
      return baseResponse;
    }

    // Get highlight information
    const highlightResult = await getRecipeHighlight(recipeId, organizationId);
    const highlight = highlightResult ? highlightResult.highlight : {};
    const hasRecent = await hasRecentChanges(recipeId, organizationId);

    // Enhance response with highlight data
    return {
      ...baseResponse,
      data: {
        ...baseResponse.data,
        highlight,
        hasRecentChanges: hasRecent,
      },
    };
  } catch (error) {
    console.error("Error enhancing response with highlights:", error);
    // Return original response if highlight enhancement fails
    return baseResponse;
  }
};



// Common recipe validation
const validateRecipeAccess = async (recipeId: number, organizationId: string, transaction: any) => {
  return await Recipe.findOne({
    where: {
      id: recipeId,
      organization_id: organizationId,
      recipe_status: {
        [Op.not]: RecipeStatus.deleted,
      },
    },
    transaction,
  });
};

// Helper function to associate uploaded files with a recipe
const associateFilesWithRecipe = async (
  recipeId: number,
  resources: any[], // Can be array of item IDs or resource objects
  organizationId: string,
  userId: number,
  transaction: any
): Promise<{ success: boolean; updatedFiles: number; message: string }> => {
  if (!resources || resources.length === 0) {
    return {
      success: true,
      updatedFiles: 0,
      message: "No resources to associate"
    };
  }

  const recipeResourcesData = [];
  let validResourcesCount = 0;
  let skippedResourcesCount = 0;

  for (const resource of resources) {
    try {
      // Handle different input formats
      let resourceData: any = {};

      // Case 1: Simple item ID (number or string)
      if (typeof resource === 'number' || (typeof resource === 'string' && /^\d+$/.test(resource))) {
        const itemId = typeof resource === 'string' ? parseInt(resource, 10) : resource;
        resourceData = {
          type: "item",
          item_id: itemId,
          item_link: null,
          item_link_type: null
        };
      }
      // Case 2: Resource object with type and item_id
      else if (resource && typeof resource === 'object') {
        resourceData = {
          type: resource.type || "item",
          item_id: resource.item_id || null,
          item_link: resource.item_link || null,
          item_link_type: resource.item_link_type || null
        };
      }
      // Case 3: Invalid resource format
      else {
        skippedResourcesCount++;
        continue;
      }

      // Handle external links
      if (resourceData.type === "link" || resourceData.item_link) {
        // Determine item_link_type for external links
        let itemLinkType = "link";
        if (resourceData.item_link_type) {
          itemLinkType = resourceData.item_link_type;
        } else if (resourceData.item_link) {
          // Auto-detect link type from URL
          const url = resourceData.item_link.toLowerCase();
          if (url.includes("youtube.com")) {
            itemLinkType = "youtube";
          } else if (url.includes("vimeo.com") || url.includes(".mp4")) {
            itemLinkType = "video";
          } else if (url.includes("spotify.com") || url.includes(".mp3") || url.includes(".wav")) {
            itemLinkType = "audio";
          } else if (url.includes(".pdf")) {
            itemLinkType = "pdf";
          } else if (url.includes(".jpg") || url.includes(".png") || url.includes(".gif") || url.includes("imgur.com")) {
            itemLinkType = "image";
          } else if (url.includes(".doc") || url.includes(".txt")) {
            itemLinkType = "text";
          } else {
            itemLinkType = "other";
          }
        }

        recipeResourcesData.push({
          recipe_id: recipeId,
          type: "link",
          item_id: null,
          item_link: resourceData.item_link,
          item_link_type: itemLinkType,
          status: RecipeResourceStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        });
        validResourcesCount++;
      }
      // Handle uploaded files (item type)
      else if (resourceData.type === "item" || resourceData.item_id) {
        // Find the Item that belongs to the user's organization
        const item = await db.Item.findOne({
          where: {
            id: resourceData.item_id,
            item_organization_id: organizationId,
            item_status: "active"
          },
          transaction,
        });

        if (!item) {
          skippedResourcesCount++;
          continue; // Skip invalid items
        }

        // Determine item_link_type based on item type
        let itemLinkType;
        if (item.item_type === "image") {
          itemLinkType = "image";
        } else if (item.item_type === "video") {
          itemLinkType = "video";
        } else if (item.item_type === "audio") {
          itemLinkType = "audio";
        } else if (item.item_type === "pdf") {
          itemLinkType = "pdf";
        } else {
          itemLinkType = "document";
        }

        recipeResourcesData.push({
          recipe_id: recipeId,
          type: "item",
          item_id: item.id,
          item_link: null,
          item_link_type: itemLinkType,
          status: RecipeResourceStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        });
        validResourcesCount++;
      }
    } catch (error) {
      // Log error but continue processing other resources
      skippedResourcesCount++;
      continue;
    }
  }

  if (recipeResourcesData.length === 0) {
    return {
      success: false,
      updatedFiles: 0,
      message: `No valid resources found to associate. Processed: ${validResourcesCount}, Skipped: ${skippedResourcesCount}`
    };
  }

  try {
    // Create the RecipeResources records
    const createdResources = await RecipeResources.bulkCreate(recipeResourcesData, { transaction });

    return {
      success: true,
      updatedFiles: createdResources.length,
      message: `Successfully associated ${createdResources.length} resources with recipe. Processed: ${validResourcesCount}, Skipped: ${skippedResourcesCount}`
    };
  } catch (error) {
    return {
      success: false,
      updatedFiles: 0,
      message: `Failed to create recipe resources: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

// ============================================================================
// API 1: BASIC RECIPE INFORMATION
// Handles: basic recipe info, categories, dietary_attributes
// ============================================================================

/**
 * @description Handle the basic information step of recipe creation or update
 * @route POST /api/v1/recipes/batch/basic-info
 * @access Private
 * @functionality Creates new recipe or updates existing recipe with basic info, categories, and dietary attributes
 */
const createRecipeBasicInfo = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();

    // Extract basic recipe information fields
    const {
      recipe_title,
      recipe_public_title,
      recipe_description,
      recipe_preparation_time,
      recipe_cook_time,
      has_recipe_public_visibility,
      has_recipe_private_visibility,
      recipe_status,
      recipe_complexity_level,
      categories,
      dietary_attributes,
      recipe_id
    } = sanitizedBody;

    const user = (req as any).user;
    const { id: userId, organization_id: organizationId } = user;

    // Check if user has permission to create recipes
    const hasPermission = await validateModulePermission(
      user,
      organizationId,
      MODULE_SLUGS.RECIPE,
      PERMISSION_TYPES.CREATE,
      getPlatformFromRequest(req)
    );

    if (!hasPermission) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Permission denied",
      });
    }

    // Validate required fields
    if (!recipe_title) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Recipe title is required",
      });
    }



    let targetRecipe: any;
    let isUpdate = false;
    let recipe_slug: string;
    let oldRecipeData: any = null;

    // Check if this is an update operation
    if (recipe_id) {
      // UPDATE OPERATION: Validate recipe exists and user has access
      targetRecipe = await validateRecipeAccess(recipe_id, organizationId, transaction);
      if (!targetRecipe) {
        return res.status(StatusCodes.NOT_FOUND).json({
          status: false,
          message: "Recipe not found or access denied",
        });
      }
      isUpdate = true;

      // Capture old recipe data for history tracking
      oldRecipeData = await captureOldRecipeData(recipe_id, transaction);

      if (!oldRecipeData) {
        return res.status(StatusCodes.NOT_FOUND).json({
          status: false,
          message: "Recipe not found for update",
        });
      }

      // For updates, check if slug needs to be regenerated (only if title changed)
      if (targetRecipe.recipe_title !== recipe_title) {
        const checkSlugExists = async (slug: string): Promise<boolean> => {
          const existingRecipe = await Recipe.findOne({
            where: {
              recipe_slug: slug,
              organization_id: organizationId,
              recipe_status: {
                [Op.not]: RecipeStatus.deleted,
              },
              id: {
                [Op.not]: recipe_id, // Exclude current recipe from slug check
              },
            },
            transaction,
          });
          return !!existingRecipe;
        };

        recipe_slug = await generateUniqueSlug(
          recipe_title,
          checkSlugExists,
          {
            maxLength: 25,
            separator: "-",
            lowercase: true,
          }
        );
      } else {
        // Keep existing slug if title hasn't changed
        recipe_slug = targetRecipe.recipe_slug;
      }
    } else {
      // CREATE OPERATION: Generate unique slug from recipe title
      const checkSlugExists = async (slug: string): Promise<boolean> => {
        const existingRecipe = await Recipe.findOne({
          where: {
            recipe_slug: slug,
            organization_id: organizationId,
            recipe_status: {
              [Op.not]: RecipeStatus.deleted,
            },
          },
          transaction,
        });
        return !!existingRecipe;
      };

      recipe_slug = await generateUniqueSlug(
        recipe_title,
        checkSlugExists,
        {
          maxLength: 25,
          separator: "-",
          lowercase: true,
        }
      );
    }

    // Prepare recipe data
    const currentTimestamp = new Date();
    const recipeData: any = {
      updated_by: userId,
    };

    // Only add fields that are actually provided in the request
    if (recipe_title !== undefined) recipeData.recipe_title = recipe_title;
    if (recipe_public_title !== undefined) recipeData.recipe_public_title = recipe_public_title;
    if (recipe_description !== undefined) recipeData.recipe_description = recipe_description;
    if (recipe_preparation_time !== undefined) recipeData.recipe_preparation_time = recipe_preparation_time;
    if (recipe_cook_time !== undefined) recipeData.recipe_cook_time = recipe_cook_time;
    if (has_recipe_public_visibility !== undefined) recipeData.has_recipe_public_visibility = has_recipe_public_visibility;
    if (has_recipe_private_visibility !== undefined) recipeData.has_recipe_private_visibility = has_recipe_private_visibility;
    if (recipe_status !== undefined) recipeData.recipe_status = recipe_status;
    if (recipe_complexity_level !== undefined) recipeData.recipe_complexity_level = recipe_complexity_level;

    // For new recipes, set required defaults
    if (!isUpdate) {
      recipeData.recipe_slug = recipe_slug;
      recipeData.ingredient_costs_updated_at = currentTimestamp;
      recipeData.nutrition_values_updated_at = currentTimestamp;
      recipeData.organization_id = organizationId;
      recipeData.created_by = userId;
      // Set defaults for required fields only for new recipes
      if (recipeData.has_recipe_public_visibility === undefined) recipeData.has_recipe_public_visibility = false;
      if (recipeData.has_recipe_private_visibility === undefined) recipeData.has_recipe_private_visibility = false;
      if (recipeData.recipe_status === undefined) recipeData.recipe_status = RecipeStatus.draft;
    }

    if (isUpdate) {
      // UPDATE OPERATION: Update existing recipe
      await Recipe.update(recipeData, {
        where: { id: recipe_id },
        transaction,
      });
      targetRecipe = { ...targetRecipe.dataValues, ...recipeData, id: recipe_id };
    } else {
      // CREATE OPERATION: Create new recipe
      targetRecipe = await Recipe.create(recipeData, { transaction });
    }

    // Handle categories if provided
    if (categories && Array.isArray(categories) && categories.length > 0) {
      if (isUpdate) {
        // For updates: Set existing categories to inactive first
        await db.RecipeCategory.update(
          {
            status: "inactive",
            updated_by: userId
          },
          {
            where: { recipe_id: targetRecipe.id },
            transaction,
          }
        );
      }

      // Process each category individually to handle existing records
      for (const categoryId of categories) {
        const existingCategory = await db.RecipeCategory.findOne({
          where: {
            recipe_id: targetRecipe.id,
            category_id: categoryId
          },
          transaction
        });

        if (existingCategory) {
          // Update existing record to active
          await db.RecipeCategory.update(
            {
              status: "active",
              updated_by: userId
            },
            {
              where: {
                recipe_id: targetRecipe.id,
                category_id: categoryId
              },
              transaction
            }
          );
        } else {
          // Create new record
          await db.RecipeCategory.create({
            recipe_id: targetRecipe.id,
            category_id: categoryId,
            status: "active",
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          }, { transaction });
        }
      }
    }

    // Handle dietary attributes if provided
    if (dietary_attributes && dietary_attributes.length > 0) {
      if (isUpdate) {
        // For updates: Set existing dietary attributes to inactive first
        // Get dietary attribute IDs to identify which ones to deactivate
        const dietaryAttributeIds = await db.sequelize.query(
          `SELECT id FROM mo_food_attributes WHERE attribute_type = 'dietary'`,
          {
            type: db.sequelize.QueryTypes.SELECT,
            transaction
          }
        ).then((results: any[]) => results.map(r => r.id));

        if (dietaryAttributeIds.length > 0) {
          await RecipeAttributes.update(
            {
              status: RecipeAttributesStatus.inactive,
              updated_by: userId
            },
            {
              where: {
                recipe_id: targetRecipe.id,
                attributes_id: {
                  [Op.in]: dietaryAttributeIds
                }
              },
              transaction
            }
          );
        }
      }

      // Process each dietary attribute individually to handle existing records
      for (const attrId of dietary_attributes) {
        const existingAttribute = await RecipeAttributes.findOne({
          where: {
            recipe_id: targetRecipe.id,
            attributes_id: attrId,
            may_contain: false,
            use_default: false
          },
          transaction
        });

        if (existingAttribute) {
          // Update existing record to active
          await RecipeAttributes.update(
            {
              status: RecipeAttributesStatus.active,
              updated_by: userId
            },
            {
              where: { id: existingAttribute.id },
              transaction
            }
          );
        } else {
          // Create new record
          await RecipeAttributes.create({
            recipe_id: targetRecipe.id,
            attributes_id: attrId,
            unit_of_measure: null,
            unit: null,
            may_contain: false, // Required for unique constraint
            use_default: false, // Required for unique constraint
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            attribute_description: null,
            created_by: userId,
            updated_by: userId,
          }, { transaction });
        }
      }
    }

    // For updates, detect what actually changed
    const changedFields: string[] = [];
    const oldValues: any = {};
    const newValues: any = {};

    if (isUpdate && oldRecipeData) {
      console.log(`DEBUG: Starting change detection for recipe ${recipe_id}`);
      console.log(`DEBUG: Request body fields:`, Object.keys(sanitizedBody));
      // Simple approach: check each field individually if it's provided in request
      if (recipe_title !== undefined && oldRecipeData.recipe_title !== recipe_title) {
        console.log(`DEBUG: Title change detected - Old: "${oldRecipeData.recipe_title}" | New: "${recipe_title}"`);
        changedFields.push('recipe_title');
        oldValues.recipe_title = oldRecipeData.recipe_title;
        newValues.recipe_title = recipe_title;
      }

      if (recipe_public_title !== undefined && oldRecipeData.recipe_public_title !== recipe_public_title) {
        console.log(`DEBUG: Public title change detected - Old: "${oldRecipeData.recipe_public_title}" | New: "${recipe_public_title}"`);
        changedFields.push('recipe_public_title');
        oldValues.recipe_public_title = oldRecipeData.recipe_public_title;
        newValues.recipe_public_title = recipe_public_title;
      }

      if (recipe_description !== undefined && oldRecipeData.recipe_description !== recipe_description) {
        console.log(`DEBUG: Description change detected - Old: "${oldRecipeData.recipe_description}" | New: "${recipe_description}"`);
        changedFields.push('recipe_description');
        oldValues.recipe_description = oldRecipeData.recipe_description;
        newValues.recipe_description = recipe_description;
      }

      if (recipe_preparation_time !== undefined && oldRecipeData.recipe_preparation_time !== recipe_preparation_time) {
        changedFields.push('recipe_preparation_time');
        oldValues.recipe_preparation_time = oldRecipeData.recipe_preparation_time;
        newValues.recipe_preparation_time = recipe_preparation_time;
      }

      if (recipe_cook_time !== undefined && oldRecipeData.recipe_cook_time !== recipe_cook_time) {
        changedFields.push('recipe_cook_time');
        oldValues.recipe_cook_time = oldRecipeData.recipe_cook_time;
        newValues.recipe_cook_time = recipe_cook_time;
      }

      // Convert boolean values for comparison to prevent false positives
      const oldPublicVisibility = Boolean(oldRecipeData.has_recipe_public_visibility);
      const newPublicVisibility = Boolean(has_recipe_public_visibility);
      if (has_recipe_public_visibility !== undefined && oldPublicVisibility !== newPublicVisibility) {
        console.log(`DEBUG: Public visibility change detected - Old: ${oldPublicVisibility} | New: ${newPublicVisibility}`);
        changedFields.push('has_recipe_public_visibility');
        oldValues.has_recipe_public_visibility = oldRecipeData.has_recipe_public_visibility;
        newValues.has_recipe_public_visibility = has_recipe_public_visibility;
      }

      const oldPrivateVisibility = Boolean(oldRecipeData.has_recipe_private_visibility);
      const newPrivateVisibility = Boolean(has_recipe_private_visibility);
      if (has_recipe_private_visibility !== undefined && oldPrivateVisibility !== newPrivateVisibility) {
        console.log(`DEBUG: Private visibility change detected - Old: ${oldPrivateVisibility} | New: ${newPrivateVisibility}`);
        changedFields.push('has_recipe_private_visibility');
        oldValues.has_recipe_private_visibility = oldRecipeData.has_recipe_private_visibility;
        newValues.has_recipe_private_visibility = has_recipe_private_visibility;
      }

      // Only detect status change if it's explicitly different
      if (recipe_status !== undefined &&
        oldRecipeData.recipe_status !== recipe_status &&
        recipe_status !== '') { // Ignore empty status values
        console.log(`DEBUG: Status change detected - Old: ${oldRecipeData.recipe_status} | New: ${recipe_status}`);
        changedFields.push('recipe_status');
        oldValues.recipe_status = oldRecipeData.recipe_status;
        newValues.recipe_status = recipe_status;
      }

      if (recipe_complexity_level !== undefined && oldRecipeData.recipe_complexity_level !== recipe_complexity_level) {
        changedFields.push('recipe_complexity_level');
        oldValues.recipe_complexity_level = oldRecipeData.recipe_complexity_level;
        newValues.recipe_complexity_level = recipe_complexity_level;
      }

      // Check recipe_placeholder if provided
      if (req.body.recipe_placeholder !== undefined && oldRecipeData.recipe_placeholder !== req.body.recipe_placeholder) {
        changedFields.push('recipe_placeholder');
        oldValues.recipe_placeholder = oldRecipeData.recipe_placeholder;
        newValues.recipe_placeholder = req.body.recipe_placeholder;
      }

      // Check recipe_slug if provided
      if (req.body.recipe_slug !== undefined && oldRecipeData.recipe_slug !== req.body.recipe_slug) {
        changedFields.push('recipe_slug');
        oldValues.recipe_slug = oldRecipeData.recipe_slug;
        newValues.recipe_slug = req.body.recipe_slug;
      }

      // Check categories if provided
      if (categories && Array.isArray(categories)) {
        // Get old categories with detailed information
        const oldCategoryIds = oldRecipeData.categories
          ? oldRecipeData.categories.map((cat: any) => cat.category_id)
          : [];

        const oldCategoriesDetailed = oldCategoryIds.length > 0
          ? await Promise.all(
            oldCategoryIds.map(async (catId: number) => {
              const catDetails = await db.sequelize.query(`
                  SELECT id, category_name, category_slug, category_status
                  FROM mo_category
                  WHERE id = :catId AND category_status = 'active'
                `, {
                replacements: { catId },
                type: db.sequelize.QueryTypes.SELECT,
                transaction,
              });
              const cat = catDetails[0] || { id: catId, category_name: 'Unknown', category_slug: 'unknown', category_status: 'active' };
              return {
                id: cat.id,
                category_name: cat.category_name,
                category_slug: cat.category_slug,
                category_status: cat.category_status,
                item_detail: {}
              };
            })
          )
          : [];

        // Get new categories with detailed information
        const newCategoriesDetailed = await Promise.all(
          categories.map(async (catId: number) => {
            const catDetails = await db.sequelize.query(`
              SELECT id, category_name, category_slug, category_status
              FROM mo_category
              WHERE id = :catId AND category_status = 'active'
            `, {
              replacements: { catId },
              type: db.sequelize.QueryTypes.SELECT,
              transaction,
            });
            const cat = catDetails[0] || { id: catId, category_name: 'Unknown', category_slug: 'unknown', category_status: 'active' };
            return {
              id: cat.id,
              category_name: cat.category_name,
              category_slug: cat.category_slug,
              category_status: cat.category_status,
              item_detail: {}
            };
          })
        );

        // Compare arrays by IDs to see if they're different
        const oldCatIds = oldCategoriesDetailed.map((cat: any) => cat.id).sort();
        const newCatIds = [...categories].sort(); // Create a copy to avoid mutating original
        const categoriesChanged = JSON.stringify(oldCatIds) !== JSON.stringify(newCatIds);

        if (categoriesChanged) {
          console.log(`DEBUG: Categories change detected - Old IDs: [${oldCatIds.join(', ')}] | New IDs: [${newCatIds.join(', ')}]`);
          changedFields.push('categories');
          oldValues.categories = oldCategoriesDetailed;
          newValues.categories = newCategoriesDetailed;
        }
      }

      // Check dietary_attributes if provided
      if (dietary_attributes && Array.isArray(dietary_attributes)) {
        // Get old dietary attributes with detailed information
        const oldDietaryAttributeIds = oldRecipeData.attributes
          ? oldRecipeData.attributes
            .filter((attr: any) => attr.attribute_type === 'dietary' && attr.status === 'active')
            .map((attr: any) => attr.attributes_id)
          : [];

        const oldDietaryAttributesDetailed = oldDietaryAttributeIds.length > 0
          ? await Promise.all(
            oldDietaryAttributeIds.map(async (attrId: number) => {
              const attrDetails = await db.sequelize.query(`
                  SELECT id, attribute_title, attribute_slug, attribute_type
                  FROM mo_food_attributes
                  WHERE id = :attrId AND attribute_status = 'active'
                `, {
                replacements: { attrId },
                type: db.sequelize.QueryTypes.SELECT,
                transaction,
              });
              const attr = attrDetails[0] || { id: attrId, attribute_title: 'Unknown', attribute_slug: 'unknown', attribute_type: 'dietary' };
              return {
                id: attr.id,
                attribute_title: attr.attribute_title,
                attribute_slug: attr.attribute_slug,
                attribute_type: attr.attribute_type,
                item_detail: {}
              };
            })
          )
          : [];

        // Get new dietary attributes with detailed information
        const newDietaryAttributesDetailed = await Promise.all(
          dietary_attributes.map(async (attrId: number) => {
            const attrDetails = await db.sequelize.query(`
              SELECT id, attribute_title, attribute_slug, attribute_type
              FROM mo_food_attributes
              WHERE id = :attrId AND attribute_status = 'active'
            `, {
              replacements: { attrId },
              type: db.sequelize.QueryTypes.SELECT,
              transaction,
            });
            const attr = attrDetails[0] || { id: attrId, attribute_title: 'Unknown', attribute_slug: 'unknown', attribute_type: 'dietary' };
            return {
              id: attr.id,
              attribute_title: attr.attribute_title,
              attribute_slug: attr.attribute_slug,
              attribute_type: attr.attribute_type,
              item_detail: {}
            };
          })
        );

        // Compare arrays by IDs to see if they're different
        const oldIds = oldDietaryAttributesDetailed.map((attr: any) => attr.id).sort();
        const newIds = dietary_attributes.sort();
        const dietaryAttributesChanged = JSON.stringify(oldIds) !== JSON.stringify(newIds);

        if (dietaryAttributesChanged) {
          changedFields.push('dietary_attributes');
          oldValues.dietary_attributes = oldDietaryAttributesDetailed;
          newValues.dietary_attributes = newDietaryAttributesDetailed;
        }
      }
    } else {
      // For new recipes, capture all provided values
      if (recipe_title !== undefined) newValues.recipe_title = recipe_title;
      if (recipe_public_title !== undefined) newValues.recipe_public_title = recipe_public_title;
      if (recipe_description !== undefined) newValues.recipe_description = recipe_description;
      if (recipe_preparation_time !== undefined) newValues.recipe_preparation_time = recipe_preparation_time;
      if (recipe_cook_time !== undefined) newValues.recipe_cook_time = recipe_cook_time;
      if (has_recipe_public_visibility !== undefined) newValues.has_recipe_public_visibility = has_recipe_public_visibility;
      if (has_recipe_private_visibility !== undefined) newValues.has_recipe_private_visibility = has_recipe_private_visibility;
      if (recipe_status !== undefined) newValues.recipe_status = recipe_status;
      if (recipe_complexity_level !== undefined) newValues.recipe_complexity_level = recipe_complexity_level;
      if (categories && Array.isArray(categories)) newValues.categories = categories;
      if (dietary_attributes && Array.isArray(dietary_attributes)) newValues.dietary_attributes = dietary_attributes;
    }

    // Create specific history entries for different types of changes
    if (!isUpdate) {
      // For new recipes, create a single creation history entry with detailed description
      const creationDetails: string[] = [];
      if (recipe_title) creationDetails.push(`Title: "${recipe_title}"`);
      if (recipe_description) creationDetails.push(`Description: "${recipe_description.substring(0, 100)}${recipe_description.length > 100 ? '...' : ''}"`);
      if (recipe_preparation_time) creationDetails.push(`Prep time: ${recipe_preparation_time} minutes`);
      if (recipe_cook_time) creationDetails.push(`Cook time: ${recipe_cook_time} minutes`);
      if (recipe_complexity_level) creationDetails.push(`Complexity: ${recipe_complexity_level}`);
      if (categories && categories.length > 0) creationDetails.push(`Categories: ${categories.length} assigned`);
      if (dietary_attributes && dietary_attributes.length > 0) creationDetails.push(`Dietary attributes: ${dietary_attributes.length} assigned`);

      const description = `Recipe "${recipe_title}" created with:\n${creationDetails.join(';\n')}.`;

      await createRecipeHistory({
        recipe_id: targetRecipe.id,
        action: RecipeHistoryAction.created,
        field_name: "recipe_created",
        new_value: JSON.stringify(newValues),
        description,
        ip_address: req.ip,
        user_agent: req.get("User-Agent") || "",
        organization_id: organizationId,
        created_by: userId,
      }, transaction);
    } else if (isUpdate && changedFields.length > 0) {
      // Debug: Log what fields are detected as changed
      console.log(`DEBUG: Recipe ${targetRecipe.id} - Changed fields detected:`, changedFields);
      console.log(`DEBUG: Old values:`, oldValues);
      console.log(`DEBUG: New values:`, newValues);

      // More robust filtering: only track changes for fields that were explicitly provided AND actually changed
      const explicitlyProvidedFields = Object.keys(req.body);
      console.log(`DEBUG: Explicitly provided fields in request:`, explicitlyProvidedFields);

      // Additional check: only include fields that have meaningful changes
      const meaningfulChangedFields = changedFields.filter(field => {
        const isExplicitlyProvided = explicitlyProvidedFields.includes(field);
        const oldVal = oldValues[field];
        const newVal = newValues[field];

        // Skip if not explicitly provided
        if (!isExplicitlyProvided) {
          console.log(`DEBUG: Skipping ${field} - not explicitly provided in request`);
          return false;
        }

        // For boolean fields, ensure they're actually different
        if (field.includes('visibility')) {
          const actuallyChanged = Boolean(oldVal) !== Boolean(newVal);
          if (!actuallyChanged) {
            console.log(`DEBUG: Skipping ${field} - boolean values are the same (${Boolean(oldVal)} === ${Boolean(newVal)})`);
          }
          return actuallyChanged;
        }

        // For status field, ensure it's not just empty/undefined
        if (field === 'recipe_status') {
          const actuallyChanged = oldVal !== newVal && newVal && newVal.trim() !== '';
          if (!actuallyChanged) {
            console.log(`DEBUG: Skipping ${field} - status not meaningfully changed (old: "${oldVal}", new: "${newVal}")`);
          }
          return actuallyChanged;
        }

        // For arrays (categories, attributes), ensure they're actually different
        if (Array.isArray(oldVal) || Array.isArray(newVal)) {
          const oldIds = Array.isArray(oldVal) ? oldVal.map((item: any) => item.id || item).sort() : [];
          const newIds = Array.isArray(newVal) ? newVal.map((item: any) => item.id || item).sort() : [];
          const actuallyChanged = JSON.stringify(oldIds) !== JSON.stringify(newIds);
          if (!actuallyChanged) {
            console.log(`DEBUG: Skipping ${field} - arrays are the same`);
          }
          return actuallyChanged;
        }

        // For other fields, simple comparison
        const actuallyChanged = oldVal !== newVal;
        if (!actuallyChanged) {
          console.log(`DEBUG: Skipping ${field} - values are the same (${oldVal} === ${newVal})`);
        }
        return actuallyChanged;
      });

      console.log(`DEBUG: Meaningful changed fields after filtering:`, meaningfulChangedFields);

      // Skip creating history entries if no meaningful changes
      if (meaningfulChangedFields.length === 0) {
        console.log(`DEBUG: No meaningful changes detected, skipping history creation for recipe ${targetRecipe.id}`);
        // Skip history creation but continue with the rest of the function
      } else {
        // For updates, create individual history entries for each changed field
        for (const field of meaningfulChangedFields) {
          const oldVal = oldValues[field];
          const newVal = newValues[field];

          let action = RecipeHistoryAction.updated;
          let description = '';
          const fieldDisplayName = field.replace(/_/g, ' ');

          // Determine action and description based on field type
          switch (field) {
            case 'recipe_title':
              description = `Recipe title changed from "${oldVal}" to "${newVal}"`;
              break;
            case 'recipe_public_title':
              description = `Recipe public title ${oldVal ? `changed from "${oldVal}" to "${newVal}"` : `set to "${newVal}"`}`;
              break;
            case 'recipe_description': {
              const oldDesc = oldVal ? (oldVal.length > 50 ? oldVal.substring(0, 50) + '...' : oldVal) : 'none';
              const newDesc = newVal ? (newVal.length > 50 ? newVal.substring(0, 50) + '...' : newVal) : 'none';
              description = `Recipe description updated from "${oldDesc}" to "${newDesc}"`;
              break;
            }
            case 'recipe_preparation_time':
              description = `Preparation time changed from ${oldVal || 0} minutes to ${newVal || 0} minutes`;
              break;
            case 'recipe_cook_time':
              description = `Cooking time changed from ${oldVal || 0} minutes to ${newVal || 0} minutes`;
              break;
            case 'recipe_complexity_level':
              description = `Complexity level changed from "${oldVal || 'not set'}" to "${newVal}"`;
              break;
            case 'recipe_placeholder':
              description = `Placeholder image ${oldVal ? `changed (ID: ${oldVal} → ${newVal})` : `set (ID: ${newVal})`}`;
              break;
            case 'recipe_slug':
              description = `URL slug changed from "${oldVal || 'not set'}" to "${newVal}"`;
              break;
            case 'recipe_status':
              action = newVal === 'publish' ? RecipeHistoryAction.published :
                newVal === 'archived' ? RecipeHistoryAction.archived :
                  RecipeHistoryAction.updated;
              description = `Recipe status changed from "${oldVal}" to "${newVal}"`;
              break;
            case 'has_recipe_public_visibility':
              description = `Public visibility ${newVal ? 'enabled' : 'disabled'} (was ${oldVal ? 'enabled' : 'disabled'})`;
              break;
            case 'has_recipe_private_visibility':
              description = `Private visibility ${newVal ? 'enabled' : 'disabled'} (was ${oldVal ? 'enabled' : 'disabled'})`;
              break;
            case 'categories': {
              action = RecipeHistoryAction.category_added;
              const oldCategories = Array.isArray(oldVal) ? oldVal : [];
              const newCategories = Array.isArray(newVal) ? newVal : [];
              const oldCategoryNames = oldCategories.map((cat: any) => cat.category_name || 'Unknown').join(', ');
              const newCategoryNames = newCategories.map((cat: any) => cat.category_name || 'Unknown').join(', ');

              if (oldCategories.length === 0) {
                description = `Categories added: ${newCategoryNames} (${newCategories.length} items)`;
              } else if (newCategories.length === 0) {
                description = `Categories removed: ${oldCategoryNames}`;
              } else {
                description = `Categories changed from "${oldCategoryNames}" to "${newCategoryNames}" (${oldCategories.length} → ${newCategories.length} items)`;
              }
              break;
            }
            case 'dietary_attributes': {
              action = RecipeHistoryAction.attribute_added;
              const oldDietaryAttrs = Array.isArray(oldVal) ? oldVal : [];
              const newDietaryAttrs = Array.isArray(newVal) ? newVal : [];
              const oldAttrNames = oldDietaryAttrs.map((attr: any) => attr.attribute_title || 'Unknown').join(', ');
              const newAttrNames = newDietaryAttrs.map((attr: any) => attr.attribute_title || 'Unknown').join(', ');

              if (oldDietaryAttrs.length === 0) {
                description = `Dietary attributes added: ${newAttrNames} (${newDietaryAttrs.length} items)`;
              } else if (newDietaryAttrs.length === 0) {
                description = `Dietary attributes removed: ${oldAttrNames}`;
              } else {
                description = `Dietary attributes changed from "${oldAttrNames}" to "${newAttrNames}" (${oldDietaryAttrs.length} → ${newDietaryAttrs.length} items)`;
              }
              break;
            }
            default:
              description = `${fieldDisplayName} updated`;
          }

          await createRecipeHistory({
            recipe_id: targetRecipe.id,
            action,
            field_name: field,
            old_value: JSON.stringify({ [field]: oldVal }),
            new_value: JSON.stringify({ [field]: newVal }),
            description,
            ip_address: req.ip,
            user_agent: req.get("User-Agent") || "",
            organization_id: organizationId,
            created_by: userId,
          }, transaction);
        }
      }
    }

    await transactionManager.commit();

    const responseStatus = isUpdate ? StatusCodes.OK : StatusCodes.CREATED;
    const responseMessage = isUpdate
      ? "Recipe basic information updated successfully"
      : "Recipe basic information saved successfully";

    // Prepare base response
    const baseResponse = {
      status: true,
      message: responseMessage,
      data: {
        recipe_id: targetRecipe.id,
        recipe_slug: targetRecipe.recipe_slug || recipe_slug,
      },
    };

    // Enhance response with highlight data
    const enhancedResponse = await enhanceResponseWithHighlights(
      targetRecipe.id,
      organizationId,
      baseResponse
    );

    return res.status(responseStatus).json(enhancedResponse);
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error processing recipe basic information"
    );
  }
};

// ============================================================================
// API 2: INGREDIENTS, NUTRITION & SERVING DETAILS
// Handles: ingredients, allergens, nutritions, HACCP data, cuisine attributes, serving details
// ============================================================================

/**
 * @description Handle ingredients, nutrition, cuisine type data and serving details for a recipe
 * @route POST /api/v1/recipes/batch/ingredients-nutrition
 * @access Private
 * @functionality Updates recipe with ingredients, nutrition, allergens, cuisine, HACCP data, and serving details
 */
const addIngredientsNutritionCuisine = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();

    // Extract ingredients, nutrition data, and serving details
    const {
      recipe_id,
      ingredients,
      nutrition_attributes,
      allergen_attributes,
      cuisine_attributes,
      dietary_attributes,
      haccp_attributes,
      is_ingredient_cooking_method,
      is_preparation_method,
      is_cost_manual,
      // Serving details
      recipe_serve_in,
      recipe_garnish,
      recipe_head_chef_tips,
      recipe_foh_tips,
      recipe_impression,
      recipe_yield,
      recipe_yield_unit,
      recipe_total_portions,
      recipe_single_portion_size,
      recipe_serving_method,
      recipe_status
    } = sanitizedBody;




    const user = (req as any).user;
    const { id: userId, organization_id: organizationId } = user;
    // Check if user has permission to create recipes
    const hasPermission = await validateModulePermission(
      user,
      organizationId,
      MODULE_SLUGS.RECIPE,
      PERMISSION_TYPES.EDIT,
      getPlatformFromRequest(req)
    );

    if (!hasPermission) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Permission denied",
      });
    }


    // Validate recipe exists and user has access
    const recipe = await validateRecipeAccess(recipe_id, organizationId, transaction);
    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    // Capture old recipe data for history tracking
    const oldRecipeData = await captureOldRecipeData(recipe_id, transaction);

    await Recipe.update(
      {
        is_ingredient_cooking_method:
          is_ingredient_cooking_method === "true" ||
          is_ingredient_cooking_method === true,
        is_preparation_method:
          is_preparation_method === "true" || is_preparation_method === true,
        is_cost_manual:
          is_cost_manual === "true" || is_cost_manual === true,
        recipe_status: recipe_status ? recipe_status : recipe.recipe_status
      },
      {
        where: { id: recipe_id },
        transaction,
      }
    );

    // Add ingredients if provided
    if (ingredients && ingredients.length > 0) {
      // Process each ingredient with upsert logic
      for (const ingredient of ingredients) {
        const ingredientData = {
          recipe_id,
          ingredient_id: ingredient.id,
          ingredient_quantity: ingredient.quantity,
          ingredient_measure: ingredient.measure,
          ingredient_wastage: ingredient.wastage,
          ingredient_cost: ingredient.cost,
          ingredient_cooking_method: ingredient.cooking_method,
          preparation_method: ingredient.preparation_method,
          recipe_ingredient_status: RecipeIngredientsStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        };

        // Use upsert to update existing or create new
        await RecipeIngredients.upsert(ingredientData, {
          transaction,
          fields: [
            'ingredient_quantity', 'ingredient_measure', 'ingredient_wastage',
            'ingredient_cost', 'ingredient_cooking_method', 'preparation_method',
            'recipe_ingredient_status', 'updated_by', 'updated_at'
          ]
        });
      }

      // Update ingredient costs timestamp
      await updateRecipeCostTimestamp(recipe_id, transaction);
    }

    // Add nutrition attributes if provided
    if (
      nutrition_attributes && nutrition_attributes.length > 0 ||
      allergen_attributes ||
      cuisine_attributes ||
      dietary_attributes ||
      haccp_attributes
    ) {
      // Process nutrition attributes
      if (nutrition_attributes && nutrition_attributes.length > 0) {
        for (const attr of nutrition_attributes) {
          const nutritionData = {
            recipe_id,
            attributes_id: attr.id,
            unit_of_measure: attr.unit_of_measure,
            unit: attr.unit,
            attribute_description: attr.attribute_description,
            use_default: attr.use_default || false,
            may_contain: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(nutritionData, {
            transaction,
            fields: [
              'unit_of_measure', 'unit', 'attribute_description', 'use_default',
              'status', 'updated_by', 'updated_at'
            ]
          });
        }
      }

      // Process allergen "contains" attributes
      if (allergen_attributes?.contains && allergen_attributes.contains.length > 0) {
        for (const attrId of allergen_attributes.contains) {
          const allergenData = {
            recipe_id,
            attributes_id: attrId,
            may_contain: false,
            use_default: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(allergenData, {
            transaction,
            fields: ['status', 'updated_by', 'updated_at']
          });
        }
      }

      // Process allergen "may contain" attributes
      if (allergen_attributes?.may_contain && allergen_attributes.may_contain.length > 0) {
        for (const attrId of allergen_attributes.may_contain) {
          const allergenData = {
            recipe_id,
            attributes_id: attrId,
            may_contain: true,
            use_default: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(allergenData, {
            transaction,
            fields: ['status', 'updated_by', 'updated_at']
          });
        }
      }

      // Process cuisine attributes
      if (cuisine_attributes && cuisine_attributes.length > 0) {
        for (const attrId of cuisine_attributes) {
          const cuisineData = {
            recipe_id,
            attributes_id: attrId,
            may_contain: false,
            use_default: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(cuisineData, {
            transaction,
            fields: ['status', 'updated_by', 'updated_at']
          });
        }
      }

      // Process dietary attributes
      if (dietary_attributes && dietary_attributes.length > 0) {
        for (const attrId of dietary_attributes) {
          const dietaryData = {
            recipe_id,
            attributes_id: attrId,
            may_contain: false,
            use_default: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(dietaryData, {
            transaction,
            fields: ['status', 'updated_by', 'updated_at']
          });
        }
      }

      // Process HACCP attributes - Allow unlimited entries for same attribute ID
      if (haccp_attributes && haccp_attributes.length > 0) {
        // First, get all HACCP attribute IDs to identify which ones to delete
        const haccpAttributeIds = await db.sequelize.query(
          `SELECT id FROM mo_food_attributes WHERE attribute_type = 'haccp_category'`,
          {
            type: db.sequelize.QueryTypes.SELECT,
            transaction
          }
        ).then((results: any[]) => results.map(r => r.id));

        // Delete existing HACCP attributes for this recipe to allow fresh entries
        if (haccpAttributeIds.length > 0) {
          await RecipeAttributes.destroy({
            where: {
              recipe_id,
              attributes_id: {
                [Op.in]: haccpAttributeIds
              }
            },
            transaction
          });
        }

        // Create all new HACCP attribute entries (including unlimited duplicates)
        const haccpData = haccp_attributes.map((attr: any) => ({
          recipe_id,
          attributes_id: attr.id,
          unit_of_measure: null,
          unit: null,
          attribute_description: attr.attribute_description,
          use_default: attr.use_default || false,
          may_contain: false,
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        // Use bulkCreate to insert all entries (no constraints now)
        await RecipeAttributes.bulkCreate(haccpData, {
          transaction,
        });
      }

      // Update nutrition values timestamp
      await updateRecipeNutritionTimestamp(recipe_id, transaction);
    }

    // Update recipe with serving details
    const updateData: any = {
      updated_by: userId,
      updated_at: new Date()
    };

    // Add serving details if provided
    if (recipe_serve_in !== undefined) updateData.recipe_serve_in = recipe_serve_in;
    if (recipe_garnish !== undefined) updateData.recipe_garnish = recipe_garnish;
    if (recipe_head_chef_tips !== undefined) updateData.recipe_head_chef_tips = recipe_head_chef_tips;
    if (recipe_foh_tips !== undefined) updateData.recipe_foh_tips = recipe_foh_tips;
    if (recipe_impression !== undefined) updateData.recipe_impression = recipe_impression;
    if (recipe_yield !== undefined) updateData.recipe_yield = recipe_yield;
    if (recipe_yield_unit !== undefined) updateData.recipe_yield_unit = recipe_yield_unit;
    if (recipe_total_portions !== undefined) updateData.recipe_total_portions = recipe_total_portions;
    if (recipe_single_portion_size !== undefined) updateData.recipe_single_portion_size = recipe_single_portion_size;
    if (recipe_serving_method !== undefined) updateData.recipe_serving_method = recipe_serving_method;

    await recipe.update(updateData, { transaction });

    // Detect what actually changed for precise history tracking
    const changedFields: string[] = [];
    const oldValues: any = {};
    const newValues: any = {};

    // Helper function to get detailed attribute information with type-specific fields
    const getDetailedAttributes = async (attributeIds: number[], attributeType: string) => {
      if (!attributeIds || attributeIds.length === 0) return [];

      return await Promise.all(
        attributeIds.map(async (attrId: number) => {
          // Get basic attribute details
          const attrDetails = await db.sequelize.query(`
            SELECT id, attribute_title, attribute_slug, attribute_type, attribute_description
            FROM mo_food_attributes
            WHERE id = :attrId AND attribute_status = 'active'
          `, {
            replacements: { attrId },
            type: db.sequelize.QueryTypes.SELECT,
            transaction,
          });

          const attr = attrDetails[0] || {
            id: attrId,
            attribute_title: 'Unknown',
            attribute_slug: 'unknown',
            attribute_type: attributeType,
            attribute_description: null
          };

          // Get type-specific data from RecipeAttributes junction table
          const recipeAttrDetails = await db.sequelize.query(`
            SELECT unit, unit_of_measure, attribute_description, may_contain, use_default
            FROM mo_recipe_attributes
            WHERE recipe_id = :recipeId AND attributes_id = :attrId AND status = 'active'
          `, {
            replacements: { recipeId: recipe_id, attrId },
            type: db.sequelize.QueryTypes.SELECT,
            transaction,
          });

          const recipeAttr = recipeAttrDetails[0] || {};

          // Build response with consistent field names matching get recipe by id API
          const result: any = {
            id: attr.id,
            attribute_title: attr.attribute_title,
            attribute_slug: attr.attribute_slug,
            attribute_type: attr.attribute_type,
            item_detail: {}
          };

          // Add type-specific fields based on attribute type
          if (attributeType === 'nutrition') {
            result.unit = recipeAttr.unit || null;
            result.unit_of_measure = recipeAttr.unit_of_measure || null;
          } else if (attributeType === 'allergen') {
            result.may_contain = recipeAttr.may_contain || false;
          } else if (attributeType === 'haccp_category') {
            result.attribute_description = recipeAttr.attribute_description || attr.attribute_description || null;
            result.use_default = recipeAttr.use_default || false;
          }

          return result;
        })
      );
    };

    // Check nutrition_attributes if provided
    if (nutrition_attributes && Array.isArray(nutrition_attributes)) {
      const oldNutritionAttributeIds = oldRecipeData.attributes
        ? oldRecipeData.attributes
          .filter((attr: any) => attr.attribute_type === 'nutrition' && attr.status === 'active')
          .map((attr: any) => attr.attributes_id)
        : [];

      const newNutritionAttributeIds = nutrition_attributes.map((attr: any) => attr.id);
      const oldNutritionAttributesDetailed = await getDetailedAttributes(oldNutritionAttributeIds, 'nutrition');
      const newNutritionAttributesDetailed = await getDetailedAttributes(newNutritionAttributeIds, 'nutrition');

      const oldIds = oldNutritionAttributesDetailed.map((attr: any) => attr.id).sort();
      const newIds = newNutritionAttributeIds.sort();
      const nutritionAttributesChanged = JSON.stringify(oldIds) !== JSON.stringify(newIds);

      if (nutritionAttributesChanged) {
        changedFields.push('nutrition_attributes');
        oldValues.nutrition_attributes = oldNutritionAttributesDetailed;
        newValues.nutrition_attributes = newNutritionAttributesDetailed;
      }
    }

    // Check allergen_attributes if provided
    if (allergen_attributes && (allergen_attributes.contains || allergen_attributes.may_contain)) {
      const oldAllergenAttributeIds = oldRecipeData.attributes
        ? oldRecipeData.attributes
          .filter((attr: any) => attr.attribute_type === 'allergen' && attr.status === 'active')
          .map((attr: any) => attr.attributes_id)
        : [];

      const newAllergenAttributeIds = [
        ...(allergen_attributes.contains || []),
        ...(allergen_attributes.may_contain || [])
      ];
      const oldAllergenAttributesDetailed = await getDetailedAttributes(oldAllergenAttributeIds, 'allergen');
      const newAllergenAttributesDetailed = await getDetailedAttributes(newAllergenAttributeIds, 'allergen');

      const oldIds = oldAllergenAttributesDetailed.map((attr: any) => attr.id).sort();
      const newIds = newAllergenAttributeIds.sort();
      const allergenAttributesChanged = JSON.stringify(oldIds) !== JSON.stringify(newIds);

      if (allergenAttributesChanged) {
        changedFields.push('allergen_attributes');
        oldValues.allergen_attributes = oldAllergenAttributesDetailed;

        // Store both the detailed attributes and the original structure for history tracking
        newValues.allergen_attributes = {
          detailed: newAllergenAttributesDetailed,
          original: allergen_attributes
        };
      }
    }

    // Check cuisine_attributes if provided
    if (cuisine_attributes && Array.isArray(cuisine_attributes)) {
      const oldCuisineAttributeIds = oldRecipeData.attributes
        ? oldRecipeData.attributes
          .filter((attr: any) => attr.attribute_type === 'cuisine' && attr.status === 'active')
          .map((attr: any) => attr.attributes_id)
        : [];

      const oldCuisineAttributesDetailed = await getDetailedAttributes(oldCuisineAttributeIds, 'cuisine');
      const newCuisineAttributesDetailed = await getDetailedAttributes(cuisine_attributes, 'cuisine');

      const oldIds = oldCuisineAttributesDetailed.map((attr: any) => attr.id).sort();
      const newIds = cuisine_attributes.sort();
      const cuisineAttributesChanged = JSON.stringify(oldIds) !== JSON.stringify(newIds);

      if (cuisineAttributesChanged) {
        changedFields.push('cuisine_attributes');
        oldValues.cuisine_attributes = oldCuisineAttributesDetailed;
        newValues.cuisine_attributes = newCuisineAttributesDetailed;
      }
    }

    // Check haccp_attributes if provided
    if (haccp_attributes && Array.isArray(haccp_attributes)) {
      const oldHaccpAttributeIds = oldRecipeData.attributes
        ? oldRecipeData.attributes
          .filter((attr: any) => attr.attribute_type === 'haccp_category' && attr.status === 'active')
          .map((attr: any) => attr.attributes_id)
        : [];

      const newHaccpAttributeIds = haccp_attributes.map((attr: any) => attr.id);
      const oldHaccpAttributesDetailed = await getDetailedAttributes(oldHaccpAttributeIds, 'haccp_category');
      const newHaccpAttributesDetailed = await getDetailedAttributes(newHaccpAttributeIds, 'haccp_category');

      const oldIds = oldHaccpAttributesDetailed.map((attr: any) => attr.id).sort();
      const newIds = newHaccpAttributeIds.sort();
      const haccpAttributesChanged = JSON.stringify(oldIds) !== JSON.stringify(newIds);

      if (haccpAttributesChanged) {
        changedFields.push('haccp_attributes');
        oldValues.haccp_attributes = oldHaccpAttributesDetailed;
        newValues.haccp_attributes = newHaccpAttributesDetailed;
      }
    }

    // Check ingredient_cooking_method flag if provided
    if (is_ingredient_cooking_method !== undefined) {
      // For cooking method, we track the boolean flag change
      const oldCookingMethodValue = oldRecipeData.is_ingredient_cooking_method || false;
      const newCookingMethodValue = is_ingredient_cooking_method === "true" || is_ingredient_cooking_method === true;

      if (oldCookingMethodValue !== newCookingMethodValue) {
        changedFields.push('is_ingredient_cooking_method');
        oldValues.is_ingredient_cooking_method = oldCookingMethodValue;
        newValues.is_ingredient_cooking_method = newCookingMethodValue;
      }
    }

    // Check preparation_method flag if provided
    if (is_preparation_method !== undefined) {
      // For preparation method, we track the boolean flag change
      const oldPreparationMethodValue = oldRecipeData.is_preparation_method || false;
      const newPreparationMethodValue = is_preparation_method === "true" || is_preparation_method === true;

      if (oldPreparationMethodValue !== newPreparationMethodValue) {
        changedFields.push('is_preparation_method');
        oldValues.is_preparation_method = oldPreparationMethodValue;
        newValues.is_preparation_method = newPreparationMethodValue;
      }
    }

    // Check for changes in provided fields only
    const requestData = {
      ingredients,
      nutrition_attributes,
      allergen_attributes,
      cuisine_attributes,
      dietary_attributes,
      haccp_attributes,
      is_ingredient_cooking_method,
      is_preparation_method,
      is_cost_manual,
      recipe_serve_in,
      recipe_garnish,
      recipe_head_chef_tips,
      recipe_foh_tips,
      recipe_impression,
      recipe_yield,
      recipe_yield_unit,
      recipe_total_portions,
      recipe_single_portion_size,
      recipe_serving_method,
      // Add direct nutrition values if provided
      vitamin_a: req.body.vitamin_a,
      vitamin_c: req.body.vitamin_c,
      calcium: req.body.calcium,
      iron: req.body.iron,
    };

    Object.keys(requestData).forEach(field => {
      const newVal = (requestData as any)[field];
      if (newVal !== undefined) {
        changedFields.push(field);
        newValues[field] = newVal;

        // Capture old value if it exists
        if (oldRecipeData) {
          if (field === 'ingredients') {
            oldValues[field] = oldRecipeData.ingredients || [];
          } else if (field.includes('attributes')) {
            oldValues[field] = oldRecipeData.attributes || [];
          } else {
            oldValues[field] = oldRecipeData[field];
          }
        }
      }
    });

    // Create specific history entries for different types of changes
    if (changedFields.length > 0) {
      // Group changes by type
      const ingredientFields = ['ingredients'];
      const attributeFields = ['nutrition_attributes', 'allergen_attributes', 'cuisine_attributes', 'dietary_attributes', 'haccp_attributes'];
      const methodFields = ['is_ingredient_cooking_method', 'is_preparation_method', 'is_cost_manual'];
      const servingFields = ['recipe_serve_in', 'recipe_garnish', 'recipe_head_chef_tips', 'recipe_foh_tips', 'recipe_impression', 'recipe_yield', 'recipe_yield_unit', 'recipe_total_portions', 'recipe_single_portion_size', 'recipe_serving_method'];
      const nutritionValueFields = ['vitamin_a', 'vitamin_c', 'calcium', 'iron'];

      // Create history for ingredient changes
      const ingredientChanges = changedFields.filter(field => ingredientFields.includes(field));
      if (ingredientChanges.length > 0) {
        const ingredientOldValues: any = {};
        const ingredientNewValues: any = {};

        for (const field of ingredientChanges) {
          if (field === 'ingredients') {
            // Get detailed ingredient information for old values
            const oldIngredientsDetailed = oldValues[field] && Array.isArray(oldValues[field])
              ? await Promise.all(
                oldValues[field].map(async (ing: any) => {
                  const ingredientDetails = await db.sequelize.query(`
                      SELECT id, ingredient_name, ingredient_slug, ingredient_status
                      FROM mo_ingredients
                      WHERE id = :ingredientId AND ingredient_status = 'active'
                    `, {
                    replacements: { ingredientId: ing.ingredient_id },
                    type: db.sequelize.QueryTypes.SELECT,
                    transaction,
                  });
                  const ingredient = ingredientDetails[0] || {
                    id: ing.ingredient_id,
                    ingredient_name: 'Unknown',
                    ingredient_slug: 'unknown',
                    ingredient_status: 'unknown'
                  };
                  return {
                    id: ing.ingredient_id,
                    name: ingredient.ingredient_name,
                    slug: ingredient.ingredient_slug,
                    status: ingredient.ingredient_status,
                    quantity: ing.ingredient_quantity,
                    measure: ing.ingredient_measure,
                    cost: ing.ingredient_cost,
                    cooking_method: ing.ingredient_cooking_method,
                    preparation_method: ing.preparation_method
                  };
                })
              )
              : [];

            // Get detailed ingredient information for new values
            const newIngredientsDetailed = newValues[field] && Array.isArray(newValues[field])
              ? await Promise.all(
                newValues[field].map(async (ing: any) => {
                  const ingredientDetails = await db.sequelize.query(`
                      SELECT id, ingredient_name, ingredient_slug, ingredient_status
                      FROM mo_ingredients
                      WHERE id = :ingredientId AND ingredient_status = 'active'
                    `, {
                    replacements: { ingredientId: ing.id },
                    type: db.sequelize.QueryTypes.SELECT,
                    transaction,
                  });
                  const ingredient = ingredientDetails[0] || {
                    id: ing.id,
                    ingredient_name: 'Unknown',
                    ingredient_slug: 'unknown',
                    ingredient_status: 'unknown'
                  };
                  return {
                    id: ing.id,
                    name: ingredient.ingredient_name,
                    slug: ingredient.ingredient_slug,
                    status: ingredient.ingredient_status,
                    quantity: ing.quantity,
                    measure: ing.measure,
                    cost: ing.cost,
                    cooking_method: ing.cooking_method,
                    preparation_method: ing.preparation_method
                  };
                })
              )
              : [];

            ingredientOldValues[field] = oldIngredientsDetailed;
            ingredientNewValues[field] = newIngredientsDetailed;
          } else {
            ingredientOldValues[field] = oldValues[field];
            ingredientNewValues[field] = newValues[field];
          }
        }

        // Generate detailed description for ingredient changes
        const oldIngredients = ingredientOldValues.ingredients || [];
        const newIngredients = ingredientNewValues.ingredients || [];

        let description = `Recipe "${recipe.recipe_title}" ingredients updated:\n`;

        if (oldIngredients.length === 0) {
          const ingredientList = newIngredients.map((ing: any) =>
            `${ing.name} (${ing.quantity || 0} ${ing.measure || 'units'})`
          ).join(', ');
          description += `Ingredients added: ${ingredientList} (${newIngredients.length} ingredients)`;
        } else if (newIngredients.length === 0) {
          const oldIngredientList = oldIngredients.map((ing: any) => ing.name || 'Unknown').join(', ');
          description += `All ingredients removed (previously: ${oldIngredientList})`;
        } else {
          const oldIngredientNames = oldIngredients.map((ing: any) => ing.name || 'Unknown').join(', ');
          const newIngredientNames = newIngredients.map((ing: any) => ing.name || 'Unknown').join(', ');
          description += `Ingredients changed from "${oldIngredientNames}" to "${newIngredientNames}"`;
          description += `\n(${oldIngredients.length} → ${newIngredients.length} ingredients)`;

          // Add cost impact if available
          const oldTotalCost = oldIngredients.reduce((sum: number, ing: any) => sum + (ing.cost || 0), 0);
          const newTotalCost = newIngredients.reduce((sum: number, ing: any) => sum + (ing.cost || 0), 0);
          if (oldTotalCost !== newTotalCost) {
            description += `\nCost impact: $${oldTotalCost.toFixed(2)} → $${newTotalCost.toFixed(2)}`;
          }
        }

        await createRecipeHistory({
          recipe_id,
          action: RecipeHistoryAction.ingredient_updated,
          field_name: "ingredients",
          old_value: JSON.stringify(ingredientOldValues),
          new_value: JSON.stringify(ingredientNewValues),
          description,
          ip_address: req.ip,
          user_agent: req.get("User-Agent") || "",
          organization_id: organizationId,
          created_by: userId,
        }, transaction);
      }

      // Create history for attribute changes
      const attributeChanges = changedFields.filter(field => attributeFields.includes(field));
      if (attributeChanges.length > 0) {
        const attributeOldValues: any = {};
        const attributeNewValues: any = {};
        attributeChanges.forEach(field => {
          attributeOldValues[field] = oldValues[field];
          attributeNewValues[field] = newValues[field];
        });

        // Generate detailed description for attribute changes
        const attributeDescriptions: string[] = [];

        attributeChanges.forEach(field => {
          const oldAttrs = attributeOldValues[field] || [];
          const newAttrs = attributeNewValues[field] || [];

          // Special handling for allergen_attributes which has a different structure
          const safeOldAttrs = Array.isArray(oldAttrs) ? oldAttrs : [];
          let safeNewAttrs = Array.isArray(newAttrs) ? newAttrs : [];

          if (field === 'allergen_attributes' && newAttrs && typeof newAttrs === 'object' && newAttrs.detailed) {
            // Use the detailed attributes for history tracking
            safeNewAttrs = newAttrs.detailed;
          }

          const fieldDisplayName = field.replace(/_/g, ' ').replace('attributes', '');
          const oldAttrNames = safeOldAttrs.map((attr: any) => attr.attribute_title || 'Unknown').join(', ');
          const newAttrNames = safeNewAttrs.map((attr: any) => attr.attribute_title || 'Unknown').join(', ');

          if (safeOldAttrs.length === 0) {
            attributeDescriptions.push(`${fieldDisplayName} added: ${newAttrNames} (${safeNewAttrs.length} items)`);
          } else if (safeNewAttrs.length === 0) {
            attributeDescriptions.push(`${fieldDisplayName} removed: ${oldAttrNames}`);
          } else {
            attributeDescriptions.push(`${fieldDisplayName} changed from "${oldAttrNames}" to "${newAttrNames}" (${safeOldAttrs.length} → ${safeNewAttrs.length} items)`);
          }

          // Add specific details for nutrition attributes
          if (field === 'nutrition_attributes') {
            const nutritionDetails = safeNewAttrs.map((attr: any) => {
              if (attr.unit && attr.unit_of_measure) {
                return `${attr.attribute_title}: ${attr.unit} ${attr.unit_of_measure}`;
              }
              return attr.attribute_title;
            }).join(', ');
            if (nutritionDetails !== newAttrNames) {
              attributeDescriptions.push(`Nutrition details: ${nutritionDetails}`);
            }
          }
        });

        const description = `Recipe "${recipe.recipe_title}" attributes updated:\n${attributeDescriptions.join(';\n')}.`;

        await createRecipeHistory({
          recipe_id,
          action: RecipeHistoryAction.attribute_added,
          field_name: "attributes",
          old_value: JSON.stringify(attributeOldValues),
          new_value: JSON.stringify(attributeNewValues),
          description,
          ip_address: req.ip,
          user_agent: req.get("User-Agent") || "",
          organization_id: organizationId,
          created_by: userId,
        }, transaction);
      }

      // Create history for nutrition value changes
      const nutritionValueChanges = changedFields.filter(field => nutritionValueFields.includes(field));
      if (nutritionValueChanges.length > 0) {
        const nutritionOldValues: any = {};
        const nutritionNewValues: any = {};
        nutritionValueChanges.forEach(field => {
          nutritionOldValues[field] = oldValues[field];
          nutritionNewValues[field] = newValues[field];
        });

        // Generate detailed description for nutrition value changes
        const nutritionDescriptions: string[] = [];
        nutritionValueChanges.forEach(field => {
          const oldVal = oldValues[field];
          const newVal = newValues[field];
          const fieldName = field.replace(/_/g, ' ').replace(/^vitamin /, 'Vitamin ');

          if (oldVal === null || oldVal === undefined) {
            nutritionDescriptions.push(`${fieldName} set to ${newVal || 0}mg`);
          } else if (newVal === null || newVal === undefined) {
            nutritionDescriptions.push(`${fieldName} removed (was ${oldVal}mg)`);
          } else {
            nutritionDescriptions.push(`${fieldName} changed from ${oldVal}mg to ${newVal}mg`);
          }
        });

        const description = `Recipe "${recipe.recipe_title}" nutrition values updated:\n${nutritionDescriptions.join(';\n')}.`;

        await createRecipeHistory({
          recipe_id,
          action: RecipeHistoryAction.updated,
          field_name: "nutrition_values",
          old_value: JSON.stringify(nutritionOldValues),
          new_value: JSON.stringify(nutritionNewValues),
          description,
          ip_address: req.ip,
          user_agent: req.get("User-Agent") || "",
          organization_id: organizationId,
          created_by: userId,
        }, transaction);
      }

      // Create history for method and serving changes
      const otherChanges = changedFields.filter(field => methodFields.includes(field) || servingFields.includes(field));
      if (otherChanges.length > 0) {
        const otherOldValues: any = {};
        const otherNewValues: any = {};
        otherChanges.forEach(field => {
          otherOldValues[field] = oldValues[field];
          otherNewValues[field] = newValues[field];
        });

        // Generate detailed description for method and serving changes
        const methodFields = ['is_ingredient_cooking_method', 'is_preparation_method', 'is_cost_manual'];
        const servingFields = ['recipe_serve_in', 'recipe_garnish', 'recipe_head_chef_tips', 'recipe_foh_tips',
          'recipe_impression', 'recipe_yield', 'recipe_yield_unit', 'recipe_total_portions',
          'recipe_single_portion_size', 'recipe_serving_method'];

        const methodDescriptions: string[] = [];
        const servingDescriptions: string[] = [];

        otherChanges.forEach(field => {
          const oldVal = otherOldValues[field];
          const newVal = otherNewValues[field];

          if (methodFields.includes(field)) {
            switch (field) {
              case 'is_ingredient_cooking_method':
                methodDescriptions.push(`Cooking method tracking ${newVal ? 'enabled' : 'disabled'} (was ${oldVal ? 'enabled' : 'disabled'})`);
                break;
              case 'is_preparation_method':
                methodDescriptions.push(`Preparation method tracking ${newVal ? 'enabled' : 'disabled'} (was ${oldVal ? 'enabled' : 'disabled'})`);
                break;
              case 'is_cost_manual':
                methodDescriptions.push(`Manual cost override ${newVal ? 'enabled' : 'disabled'} (was ${oldVal ? 'enabled' : 'disabled'})`);
                break;
            }
          } else if (servingFields.includes(field)) {
            switch (field) {
              case 'recipe_yield':
                servingDescriptions.push(`Yield changed from ${oldVal || 0} to ${newVal || 0}`);
                break;
              case 'recipe_yield_unit':
                servingDescriptions.push(`Yield unit changed from "${oldVal || 'not set'}" to "${newVal}"`);
                break;
              case 'recipe_total_portions':
                servingDescriptions.push(`Total portions changed from ${oldVal || 0} to ${newVal || 0}`);
                break;
              case 'recipe_single_portion_size':
                servingDescriptions.push(`Single portion size changed from ${oldVal || 0} to ${newVal || 0}`);
                break;
              case 'recipe_serve_in':
                servingDescriptions.push(`Serving style changed from "${oldVal || 'not set'}" to "${newVal}"`);
                break;
              case 'recipe_serving_method':
                servingDescriptions.push(`Serving method changed from "${oldVal || 'not set'}" to "${newVal}"`);
                break;
              default: {
                const fieldName = field.replace(/^recipe_/, '').replace(/_/g, ' ');
                servingDescriptions.push(`${fieldName} updated`);
                break;
              }
            }
          }
        });

        let description = `Recipe "${recipe.recipe_title}" settings updated:\n`;
        if (methodDescriptions.length > 0) {
          description += `Method settings: ${methodDescriptions.join('; ')}\n`;
        }
        if (servingDescriptions.length > 0) {
          description += `Serving details: ${servingDescriptions.join('; ')}`;
        }

        await createRecipeHistory({
          recipe_id,
          action: RecipeHistoryAction.updated,
          field_name: "recipe_details",
          old_value: JSON.stringify(otherOldValues),
          new_value: JSON.stringify(otherNewValues),
          description,
          ip_address: req.ip,
          user_agent: req.get("User-Agent") || "",
          organization_id: organizationId,
          created_by: userId,
        }, transaction);
      }
    }

    await transactionManager.commit();

    // Prepare base response
    const baseResponse = {
      status: true,
      message: "Recipe ingredients, nutrition, cuisine data, and serving details saved successfully",
      data: { recipe_id },
    };

    // Enhance response with highlight data
    const enhancedResponse = await enhanceResponseWithHighlights(
      recipe_id,
      organizationId,
      baseResponse
    );

    return res.status(StatusCodes.OK).json(enhancedResponse);
  } catch (error: unknown) {
    await transactionManager.rollback();

    // Handle specific database constraint errors
    const sqlError = error as any;
    if (sqlError.name === 'SequelizeUniqueConstraintError' ||
      (sqlError.message && sqlError.message.includes('PRIMARY already exists'))) {

      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Duplicate data detected. Please check for duplicate ingredients or attributes.",
        errorType: "DUPLICATE_ERROR",
        error: "Validation error",
      });
    }

    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error adding ingredients, nutrition data, and serving details"
    );
  }
};

// ============================================================================
// API 4: RECIPE STEPS MANAGEMENT
// Handles: recipe steps creation and updates
// ============================================================================

/**
 * @description Add or update recipe steps
 * @route POST /api/v1/recipes/batch/steps
 * @access Private
 * @functionality Processes all recipe steps at once
 */
const addRecipeSteps = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();

    // Extract steps data
    const {
      recipe_id,
      recipe_steps,
      recipe_status
    } = sanitizedBody;

    const user = (req as any).user;
    const organizationId = user?.organization_id;
    const userId = user?.id;

    // Check if user has permission to create recipes
    const hasPermission = await validateModulePermission(
      user,
      organizationId,
      MODULE_SLUGS.RECIPE,
      PERMISSION_TYPES.EDIT,
      getPlatformFromRequest(req)
    );

    if (!hasPermission) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Permission denied",
      });
    }


    // Validate recipe exists and user has access
    const recipe = await validateRecipeAccess(recipe_id, organizationId, transaction);
    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    // Capture old recipe data for history tracking
    const oldRecipeData = await captureOldRecipeData(recipe_id, transaction);

    // Validate steps data
    if (!Array.isArray(recipe_steps) || recipe_steps.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Steps array is required and must contain at least one step",
      });
    }

    // Delete existing steps to avoid unique constraint violation
    // This is necessary because of the unique constraint on (recipe_id, recipe_step_order)
    await RecipeSteps.destroy({
      where: { recipe_id },
      transaction,
    });

    // Create new step records (Fixed field mapping to match model)
    const stepsData = recipe_steps.map((step: any, index: number) => ({
      recipe_id,
      recipe_step_order: step.recipe_step_order || index + 1, // Fixed: use 'recipe_step_order' to match model
      recipe_step_description: step.recipe_step_description || step.recipe_step_description || "", // Fixed: use 'recipe_step_description' to match model
      item_id: step.item_id || null, // Optional item reference
      status: RecipeStepsStatus.active,
      organization_id: organizationId,
      created_by: userId,
      updated_by: userId,
    }));

    await RecipeSteps.bulkCreate(stepsData, { transaction });

    // Update recipe
    await recipe.update(
      {
        recipe_status: recipe_status ? recipe_status : recipe.recipe_status,
        updated_by: userId,
        updated_at: new Date()
      },
      { transaction }
    );

    // Create history entry with detailed tracking (only if steps were actually provided)
    if (recipe_steps && recipe_steps.length > 0) {
      // Format old steps with detailed information
      const oldStepsDetailed = oldRecipeData && oldRecipeData.steps
        ? oldRecipeData.steps.map((step: any) => ({
          id: step.id,
          order: step.recipe_step_order,
          description: step.recipe_step_description || '',
          item_id: step.item_id,
          status: step.status
        }))
        : [];

      // Format new steps with detailed information
      const newStepsDetailed = recipe_steps.map((step: any, index: number) => ({
        order: step.recipe_step_order || index + 1,
        description: step.recipe_step_description || step.description || '',
        item_id: step.item_id || null,
        status: 'active'
      }));

      // Generate detailed description for steps changes
      let description = `Recipe "${recipe.recipe_title}" steps updated:\n`;

      if (oldStepsDetailed.length === 0) {
        description += `${newStepsDetailed.length} steps added`;
        if (newStepsDetailed.length <= 3) {
          const stepSummary = newStepsDetailed.map((step: any, index: number) =>
            `Step ${index + 1}: ${step.description.substring(0, 50)}${step.description.length > 50 ? '...' : ''}`
          ).join('; ');
          description += `:\n${stepSummary}`;
        }
      } else if (newStepsDetailed.length === 0) {
        description += `All ${oldStepsDetailed.length} steps removed`;
      } else {
        description += `Steps changed from ${oldStepsDetailed.length} to ${newStepsDetailed.length} steps`;

        // Show first few steps if reasonable number
        if (newStepsDetailed.length <= 3) {
          const stepSummary = newStepsDetailed.map((step: any, index: number) =>
            `Step ${index + 1}: ${step.description.substring(0, 50)}${step.description.length > 50 ? '...' : ''}`
          ).join('; ');
          description += `:\n${stepSummary}`;
        }
      }

      await createRecipeHistory({
        recipe_id,
        action: RecipeHistoryAction.step_updated,
        field_name: "steps",
        old_value: JSON.stringify(oldStepsDetailed),
        new_value: JSON.stringify(newStepsDetailed),
        description,
        ip_address: req.ip,
        user_agent: req.get("User-Agent") || "",
        organization_id: organizationId,
        created_by: userId,
      }, transaction);
    }

    await transactionManager.commit();

    // Prepare base response
    const baseResponse = {
      status: true,
      message: "Recipe steps saved successfully",
      data: { recipe_id }
    };

    // Enhance response with highlight data
    const enhancedResponse = await enhanceResponseWithHighlights(
      recipe_id,
      organizationId,
      baseResponse
    );

    return res.status(StatusCodes.OK).json(enhancedResponse);
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error adding recipe steps"
    );
  }
};

// ============================================================================
// API 3: RECIPE FILE ASSOCIATION
// Handles: associating uploaded files with recipes
// ============================================================================

/**
 * @description Associate uploaded files with a recipe
 * @route POST /api/v1/recipes/batch/uploads
 * @access Private
 * @functionality Associates pre-uploaded Item records with a recipe by creating RecipeResources records
 */
const addRecipeUploads = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Start transaction
    const transaction = await transactionManager.start();

    // Parse form data
    const recipe_id = req.body.recipe_id;
    const recipeResourcesId = req.body.recipe_resources ? req.body.recipe_resources : [];
    const placeholderItemId = req.body.recipe_placeholder ? req.body.recipe_placeholder : null;
    const recipe_status = req.body.recipe_status ? req.body.recipe_status : null;

    const user = (req as any).user;
    const organizationId = user?.organization_id;
    const userId = user?.id;

    // Check if user has permission to create recipes
    const hasPermission = await validateModulePermission(
      user,
      organizationId,
      MODULE_SLUGS.RECIPE,
      PERMISSION_TYPES.EDIT,
      getPlatformFromRequest(req)
    );

    if (!hasPermission) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Permission denied",
      });
    }

    // Validate recipe_id
    if (!recipe_id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Recipe ID is required",
      });
    }

    // Ensure recipeResourcesId is an array
    if (!Array.isArray(recipeResourcesId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "recipe_resources must be an array",
      });
    }

    // Validate recipe exists and user has access
    const recipe = await validateRecipeAccess(recipe_id, organizationId, transaction);
    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    // Capture old recipe data for history tracking
    const oldRecipeData = await captureOldRecipeData(recipe_id, transaction);

    if (recipeResourcesId.length > 0) {
      // Make all existing resources inactiv
      await RecipeResources.update(
        { status: RecipeResourceStatus.inactive },
        {
          where: { recipe_id },
          transaction,
        }
      );

      // Associate pre-uploaded items with recipe (creates RecipeResources from Items)
      const associationResult = await associateFilesWithRecipe(
        recipe_id,
        recipeResourcesId,
        organizationId,
        userId,
        transaction
      );

      if (!associationResult.success) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: associationResult.message,
        });
      }

      // Update recipe
      await recipe.update(
        {
          recipe_status: recipe_status ? recipe_status : recipe.recipe_status,
          updated_by: userId,
          updated_at: new Date()
        },
        { transaction }
      );

      // Create history entry with detailed tracking (only if resources were actually provided)
      if (recipeResourcesId.length > 0) {
        // Format old resources with detailed information
        const oldResourcesDetailed = oldRecipeData && oldRecipeData.resources
          ? await Promise.all(
            oldRecipeData.resources.map(async (resource: any) => {
              if (resource.item_id) {
                // Get item details for file resources
                const itemDetails = await db.sequelize.query(`
                    SELECT id, item_name, item_type, item_mime_type, item_size, item_location
                    FROM nv_items
                    WHERE id = :itemId AND item_status = 'active'
                  `, {
                  replacements: { itemId: resource.item_id },
                  type: db.sequelize.QueryTypes.SELECT,
                  transaction,
                });
                const item = itemDetails[0] || {
                  id: resource.item_id,
                  item_name: 'Unknown',
                  item_type: 'unknown',
                  item_mime_type: 'unknown',
                  item_size: 0,
                  item_location: null
                };
                return {
                  id: resource.id,
                  type: resource.type,
                  item_id: resource.item_id,
                  item_name: item.item_name,
                  item_type: item.item_type,
                  item_mime_type: item.item_mime_type,
                  item_size: item.item_size,
                  status: resource.status
                };
              } else {
                // Link resource
                return {
                  id: resource.id,
                  type: resource.type,
                  item_link: resource.item_link,
                  item_link_type: resource.item_link_type,
                  status: resource.status
                };
              }
            })
          )
          : [];

        // Format new resources with detailed information
        const newResourcesDetailed = await Promise.all(
          recipeResourcesId.map(async (itemId: number) => {
            const itemDetails = await db.sequelize.query(`
              SELECT id, item_name, item_type, item_mime_type, item_size, item_location
              FROM nv_items
              WHERE id = :itemId AND item_status = 'active'
            `, {
              replacements: { itemId },
              type: db.sequelize.QueryTypes.SELECT,
              transaction,
            });
            const item = itemDetails[0] || {
              id: itemId,
              item_name: 'Unknown',
              item_type: 'unknown',
              item_mime_type: 'unknown',
              item_size: 0,
              item_location: null
            };
            return {
              type: 'item',
              item_id: itemId,
              item_name: item.item_name,
              item_type: item.item_type,
              item_mime_type: item.item_mime_type,
              item_size: item.item_size,
              status: 'active'
            };
          })
        );

        // Generate detailed description for resource changes
        let description = `Recipe "${recipe.recipe_title}" resources updated:\n`;

        if (oldResourcesDetailed.length === 0) {
          const resourceList = newResourcesDetailed.map((res: any) => {
            const size = res.item_size ? ` (${(res.item_size / 1024 / 1024).toFixed(1)}MB)` : '';
            return `${res.item_name}${size}`;
          }).join(', ');
          description += `${newResourcesDetailed.length} resources added: ${resourceList}`;
        } else if (newResourcesDetailed.length === 0) {
          const oldResourceList = oldResourcesDetailed.map((res: any) => res.item_name || 'Unknown').join(', ');
          description += `All resources removed (previously: ${oldResourceList})`;
        } else {
          const oldResourceNames = oldResourcesDetailed.map((res: any) => res.item_name || 'Unknown').join(', ');
          const newResourceNames = newResourcesDetailed.map((res: any) => res.item_name || 'Unknown').join(', ');
          description += `Resources changed from "${oldResourceNames}" to "${newResourceNames}"`;
          description += `\n(${oldResourcesDetailed.length} → ${newResourcesDetailed.length} resources)`;

          // Add file type summary
          const fileTypes = newResourcesDetailed.reduce((types: any, res: any) => {
            const type = res.item_type || 'unknown';
            types[type] = (types[type] || 0) + 1;
            return types;
          }, {});
          const typeSummary = Object.entries(fileTypes).map(([type, count]) => `${count} ${type}`).join(', ');
          description += `\nFile types: ${typeSummary}`;
        }

        await createRecipeHistory({
          recipe_id,
          action: RecipeHistoryAction.resource_added,
          field_name: "resources",
          old_value: JSON.stringify(oldResourcesDetailed),
          new_value: JSON.stringify(newResourcesDetailed),
          description,
          ip_address: req.ip,
          user_agent: req.get("User-Agent") || "",
          organization_id: organizationId,
          created_by: userId,
        }, transaction);
      }
    }
    if (placeholderItemId) {
      // Validate placeholder item exists and belongs to organization
      const placeholderItem = await db.Item.findOne({
        where: {
          id: placeholderItemId,
          item_organization_id: organizationId,
          item_status: "active"
        },
        transaction,
      });

      if (!placeholderItem) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: "Placeholder item not found or not accessible",
        });
      }

      // Create recipe placeholder
      await Recipe.update({
        recipe_status: recipe_status ? recipe_status : recipe.recipe_status,
        recipe_placeholder: placeholderItemId
      }, {
        where: {
          id: recipe_id
        },
        transaction
      })

    }
    // Commit transaction
    await transactionManager.commit();

    // Prepare base response
    const baseResponse = {
      status: true,
      message: "Recipe files associated successfully",
      data: { recipe_id }
    };

    // Enhance response with highlight data
    const enhancedResponse = await enhanceResponseWithHighlights(
      recipe_id,
      organizationId,
      baseResponse
    );

    return res.status(StatusCodes.OK).json(enhancedResponse);
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error associating recipe files"
    );
  }
};




// ============================================================================
// EXPORTS - FOCUSED BATCH API ENDPOINTS
// ============================================================================

/**
 * Refactored Recipe Batch Controller
 *
 * This controller has been organized into 7 focused API endpoints:
 *
 * 1. createRecipeBasicInfo - API 1: Basic recipe information, categories, dietary attributes
 * 2. addIngredientsNutritionCuisine - API 2: Ingredients, allergens, nutrition, HACCP data, cuisine attributes, serving details
 * 3. addRecipeSteps - API 4: Recipe steps management (simplified)
 * 4. addRecipeUploads - API 3: File association with recipes (simplified)
 * 5. uploadSingleFile - API 5: Single file upload using upload service - creates Item records and returns item_id
 * 6. deleteUploadedFile - API 6: Delete uploaded files by item_id
 * 7. bulkDeleteUploadedFiles - API 7: Bulk delete temporary files (for discard functionality)
 *
 * Key Features Maintained:
 * - All existing middleware (auth, validation, CORS, rate limiting)
 * - Same response formats and error handling patterns
 * - Transaction management and rollback capabilities
 * - File operation tracking and cleanup
 * - Backward compatibility with existing endpoints
 *
 * Improvements:
 * - Simplified APIs without complex batch logic
 * - Better separation of concerns
 * - Shared utility functions for common operations
 * - Consistent authorization and validation patterns
 * - Clear section organization with descriptive headers
 * - Reduced code duplication
 * - Individual file upload and delete capabilities
 * - Simplified file association process
 */
export default {
  createRecipeBasicInfo,
  addIngredientsNutritionCuisine,
  addRecipeSteps,
  addRecipeUploads
};
